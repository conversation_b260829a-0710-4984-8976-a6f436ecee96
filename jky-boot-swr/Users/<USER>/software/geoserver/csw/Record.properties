@identifier.value=prefixedName
title.value=if_then_else(isNull(title), name, title)
creator.value='GeoServer Catalog'
subject.value=keywords
abstract.value=abstract
description.value=description
date.value="metadata.date"
type.value='http://purl.org/dc/dcmitype/Dataset'
references.scheme='OGC:WMS'
references.value=strConcat('${url.wms}?service=WMS&request=GetMap&layers=', prefixedName)
#publisher.value=
#format.value=
#language.value=
#coverage.value=
#source.value=
#relation.value=
#rights.value=
#contributor.value=