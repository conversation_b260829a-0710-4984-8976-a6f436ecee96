SET DATABASE UNIQUE NAME HSQLDB979ACBBA30
SET DAT<PERSON>ASE DEFAULT RESULT MEMORY ROWS 0
SET DATABASE EVENT LOG LEVEL 0
SET DATABASE TRANSACTION CONTROL LOCKS
SET DATABASE DEFAULT ISOLATION LEVEL READ COMMITTED
SET DATABASE TRANSACTION ROLL<PERSON>CK ON CONFLICT TRUE
SET DATABASE TEXT TABLE DEFAULTS ''
SET DATABASE SQL NAMES FALSE
SET DATABASE SQL RESTRICT EXEC FALSE
SET DATABASE SQL REFERENCES FALSE
SET DATABASE SQL SIZE TRUE
SET DATABASE SQL TYPES FALSE
SET DATABASE SQL TDC DELETE TRUE
SET DATABASE SQL TDC UPDATE TRUE
SET DATABASE SQL SYS INDEX NAMES TRUE
SET DATABASE SQL CONCAT NULLS TRUE
SET DATABASE SQL UNIQUE NULLS TRUE
SET DATABASE SQL CONVERT TRUNCATE TRUE
SET DATABASE SQL AVG SCALE 0
SET DATABASE SQL DOUBLE NAN TRUE
SET FILES WRITE DELAY 500 MILLIS
SET FILES BACKUP INCREMENT TRUE
SET FILES CACHE SIZE 10000
SET FILES CACHE ROWS 50000
SET FILES SCALE 32
SET FILES LOB SCALE 32
SET FILES DEFRAG 0
SET FILES NIO TRUE
SET FILES NIO SIZE 256
SET FILES LOG TRUE
SET FILES LOG SIZE 50
SET FILES CHECK 44
SET DATABASE COLLATION "SQL_TEXT" PAD SPACE
CREATE USER SA PASSWORD DIGEST 'd41d8cd98f00b204e9800998ecf8427e'
ALTER USER SA SET LOCAL TRUE
CREATE SCHEMA PUBLIC AUTHORIZATION DBA
CREATE CACHED TABLE PUBLIC.TILESET(KEY VARCHAR(320) PRIMARY KEY,LAYER_NAME VARCHAR(128),GRIDSET_ID VARCHAR(32),BLOB_FORMAT VARCHAR(64),PARAMETERS_ID VARCHAR(41),BYTES NUMERIC(21) DEFAULT 0 NOT NULL)
CREATE INDEX TILESET_LAYER ON PUBLIC.TILESET(LAYER_NAME)
CREATE CACHED TABLE PUBLIC.TILEPAGE(KEY VARCHAR(320) PRIMARY KEY,TILESET_ID VARCHAR(320),PAGE_Z SMALLINT,PAGE_X INTEGER,PAGE_Y INTEGER,CREATION_TIME_MINUTES INTEGER,FREQUENCY_OF_USE DOUBLE,LAST_ACCESS_TIME_MINUTES INTEGER,FILL_FACTOR DOUBLE,NUM_HITS NUMERIC(64),FOREIGN KEY(TILESET_ID) REFERENCES PUBLIC.TILESET(KEY) ON DELETE CASCADE)
CREATE INDEX TILEPAGE_TILESET ON PUBLIC.TILEPAGE(TILESET_ID,FILL_FACTOR)
CREATE INDEX TILEPAGE_FREQUENCY ON PUBLIC.TILEPAGE(FREQUENCY_OF_USE)
CREATE INDEX TILEPAGE_LAST_ACCESS ON PUBLIC.TILEPAGE(LAST_ACCESS_TIME_MINUTES)
ALTER SEQUENCE SYSTEM_LOBS.LOB_ID RESTART WITH 1
SET DATABASE DEFAULT INITIAL SCHEMA PUBLIC
SET TABLE PUBLIC.TILESET INDEX '2 2 0 0 1'
GRANT USAGE ON DOMAIN INFORMATION_SCHEMA.CARDINAL_NUMBER TO PUBLIC
GRANT USAGE ON DOMAIN INFORMATION_SCHEMA.YES_OR_NO TO PUBLIC
GRANT USAGE ON DOMAIN INFORMATION_SCHEMA.CHARACTER_DATA TO PUBLIC
GRANT USAGE ON DOMAIN INFORMATION_SCHEMA.SQL_IDENTIFIER TO PUBLIC
GRANT USAGE ON DOMAIN INFORMATION_SCHEMA.TIME_STAMP TO PUBLIC
GRANT DBA TO SA
SET SCHEMA SYSTEM_LOBS
INSERT INTO BLOCKS VALUES(0,2147483647,0)
