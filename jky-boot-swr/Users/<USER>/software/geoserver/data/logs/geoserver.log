CONFIG 2025-06-23 03:50:36,110 [main][Log4J2Logger.java:211] : Logging policy: Log4J2
CONFIG 2025-06-23 03:50:36,166 [main][Log4J2Logger.java:185] : Factory implementations for category GridCoverageFactory:
  org.geotools.coverage.grid.GridCoverageFactory
CONFIG 2025-06-23 03:50:36,233 [main][Log4J2Logger.java:185] : Factory implementations for category FilterFactory:
  org.geotools.filter.FilterFactoryImpl
CONFIG 2025-06-23 03:50:36,238 [main][Log4J2Logger.java:185] : Factory implementations for category StyleFactory:
  org.geotools.styling.StyleFactoryImpl
CONFIG 2025-06-23 03:50:36,240 [main][Log4J2Logger.java:185] : Factory implementations for category FeatureFactory:
  org.geotools.feature.LenientFeatureFactoryImpl
  org.geotools.feature.ValidatingFeatureFactoryImpl
DEBUG 2025-06-23 03:50:36,359 [main][Log4J2Logger.java:216] : Extension lookup 'XStreamPersisterInitializer', prior to bean geoserverExtensions initialisation.
DEBUG 2025-06-23 03:50:36,359 [main][Log4J2Logger.java:216] : Extension lookup 'ExtensionFilter', prior to bean geoserverExtensions initialisation.
DEBUG 2025-06-23 03:50:36,360 [main][Log4J2Logger.java:216] : Extension lookup 'ExtensionProvider', but provided ApplicationContext is unset.
DEBUG 2025-06-23 03:50:36,360 [main][Log4J2Logger.java:216] : Extension lookup 'ExtensionFilter', but provided ApplicationContext is unset.
DEBUG 2025-06-23 03:50:36,368 [main][Log4J2Logger.java:216] : CONFIGURING GEOSERVER LOGGING -------------------------
23 <USER> <GROUP>:50:36 CONFIG [geoserver.logging] - Log4j 2 configuration set to DEFAULT_LOGGING.xml
23 Jun 03:50:37 CONFIG [org.geoserver] - GeoServer configuration lock is enabled
23 Jun 03:50:37 CONFIG [gwc.config] - Will look for 'geowebcache.xml' in directory '/opt/geoserver/data_dir/gwc'.
23 Jun 03:50:37 CONFIG [gwc.config] - Create configuration file '/opt/geoserver/data_dir/gwc/geowebcache.xml' from template jar:file:/usr/local/tomcat/webapps/geoserver/WEB-INF/lib/gwc-core-1.27.1.jar!/geowebcache_empty.xml
23 Jun 03:50:37 WARN   [config.XMLConfiguration] - GWC configuration validation error: cvc-elt.1.a: Cannot find the declaration of element 'gwcConfiguration'.
23 Jun 03:50:37 WARN   [config.XMLConfiguration] - Will try to use configuration anyway. Please check the order of declared elements against the schema.
23 Jun 03:50:37 CONFIG [geoserver.config] - Loading catalog /opt/geoserver/data_dir
23 Jun 03:50:37 CONFIG [config.datadir] - Catalog and configuration loader uses 10 threads out of 10 available cores.
23 Jun 03:50:37 INFO   [geoserver.config] - Read Catalog in 60.66 ms: workspaces: 0, namespaces: 0, styles: 5, stores: 0, resources: 0, layers: 0, layer groups: 0.
23 Jun 03:50:37 CONFIG [config.XMLConfiguration] - Initializing GridSets from /opt/geoserver/data_dir/gwc
23 Jun 03:50:37 CONFIG [config.XMLConfiguration] - Initializing layers from /opt/geoserver/data_dir/gwc
23 Jun 03:50:37 CONFIG [gwc.layer] - Initializing GWC configuration based on GeoServer's Catalog
23 Jun 03:50:37 CONFIG [gwc.layer] - GeoServer TileLayer store base directory is: gwc-layers
23 Jun 03:50:37 CONFIG [gwc.layer] - Loading tile layers from gwc-layers
23 Jun 03:50:37 CONFIG [gwc.layer] - Loaded 0 tile layers in 666.3 μs
23 Jun 03:50:37 CONFIG [storage.BlobStoreAggregator] - BlobStoreConfiguration /opt/geoserver/data_dir/gwc contained no blob store infos.
23 Jun 03:50:37 CONFIG [storage.DefaultStorageFinder] - Found system environment variableGEOWEBCACHE_CACHE_DIR set to /opt/geoserver/data_dir/gwc, using it as the default prefix.
23 Jun 03:50:37 CONFIG [geowebcache.GeoWebCacheDispatcher] - Invoked setServletPrefix(gwc)
23 Jun 03:50:37 CONFIG [wms.WMSService] - Will NOT recombine tiles for non-tiling clients.
23 Jun 03:50:37 CONFIG [wms.WMSService] - Will proxy requests to backend that are not getmap or getcapabilities.
23 Jun 03:50:37 CONFIG [config.datadir] - Loading GeoServer config...
23 Jun 03:50:37 CONFIG [config.datadir] - Loaded service 'WMTSInfoImpl-2f01239e:1979ae8cdc2:-7ffb', enabled
23 Jun 03:50:37 CONFIG [config.datadir] - Loaded service 'WCSInfoImpl-2f01239e:1979ae8cdc2:-7ffa', enabled
23 Jun 03:50:37 CONFIG [config.datadir] - Loaded service 'WFSInfoImpl-2f01239e:1979ae8cdc2:-7ff9', enabled
23 Jun 03:50:37 CONFIG [config.datadir] - Loaded service 'WMSInfoImpl-2f01239e:1979ae8cdc2:-7ff8', enabled
23 Jun 03:50:37 CONFIG [config.datadir] - Loaded service 'csw', enabled
23 Jun 03:50:37 CONFIG [config.datadir] - Loaded service 'WPSInfoImpl-2f01239e:1979ae8cdc2:-7ff7', enabled
23 Jun 03:50:37 CONFIG [config.datadir] - Catalog and configuration loader uses 10 threads out of 10 available cores.
23 Jun 03:50:37 CONFIG [config.datadir] - Loading workspace services and settings...
23 Jun 03:50:37 CONFIG [config.datadir] - GeoServer config (settings and services) loaded in 42.39 ms
23 Jun 03:50:37 CONFIG [gwc.config] - Initializing GeoServer specific GWC configuration from gwc-gs.xml
23 Jun 03:50:37 CONFIG [gwc.config] - Will look for 'geowebcache-diskquota.xml' in directory '/opt/geoserver/data_dir/gwc'.
23 Jun 03:50:37 CONFIG [gwc.config] - Will look for 'geowebcache-diskquota-jdbc.xml' in directory '/opt/geoserver/data_dir/gwc'.
23 Jun 03:50:37 CONFIG [diskquota.ConfigLoader] - Quota config is: /opt/geoserver/data_dir/gwc/geowebcache-diskquota.xml
23 Jun 03:50:37 CONFIG [diskquota.ConfigLoader] - Quota config is: /opt/geoserver/data_dir/gwc/geowebcache-diskquota.xml
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: ows.global=100
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: ows.wms.getmap=10
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: ows.wfs.getfeature.application/msexcel=4
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: user=6
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: ows.gwc=16
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: user.ows.wps.execute=1000/d;30s
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: user.ows.wms.getmap=30/s
23 Jun 03:50:38 INFO   [flow.config] - Loaded control-flow rule: ip=10
23 Jun 03:50:38 CONFIG [geoserver.flow] - Control-flow active with 8 flow controllers
23 Jun 03:50:38 INFO   [security.csp] - Creating csp.xml with the default configuration
23 Jun 03:50:38 INFO   [security.csp] - Creating csp_default.xml with the default configuration
23 Jun 03:50:38 CONFIG [geoserver.security] - AuthenticationCache Initialized with 1000 Max Entries, 300 seconds idle time, 600 seconds time to live and 3 concurrency level
23 Jun 03:50:38 CONFIG [geoserver.security] - AuthenticationCache Eviction Task created to run every 600 seconds
23 Jun 03:50:39 WARN   [turbojpeg.TurboJPEGMapResponse] - The turbo jpeg encoder is available for usage
23 Jun 03:50:40 INFO   [geoserver.monitor] - Monitor extension enabled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 7 bindable processes in GeoServer specific processes
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 89 bindable processes in Deprecated processes
23 Jun 03:50:40 CONFIG [geoserver.wps] - Blacklisting process vec:GroupCandidateSelection as the input groupingAttributes of type class java.lang.Object cannot be handled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 31 bindable processes in Vector processes
23 Jun 03:50:40 CONFIG [geoserver.wps] - Blacklisting process ras:ConvolveCoverage as the input kernel of type class javax.media.jai.KernelJAI cannot be handled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Blacklisting process ras:RasterZonalStatistics2 as the input zones of type class java.lang.Object cannot be handled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Blacklisting process ras:RasterZonalStatistics2 as the input nodata of type class it.geosolutions.jaiext.range.Range cannot be handled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Blacklisting process ras:RasterZonalStatistics2 as the input rangeData of type class java.lang.Object cannot be handled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Blacklisting process ras:RasterZonalStatistics2 as the output zonal statistics of type interface java.util.List cannot be handled
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 18 bindable processes in Raster processes
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 48 bindable processes in Geometry processes
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 1 bindable processes in PolygonLabelProcess
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 1 bindable processes in geo
23 Jun 03:50:40 CONFIG [geoserver.wps] - Found 1 bindable processes in geo
23 Jun 03:50:40 CONFIG [diskquota.ConfigLoader] - Quota config is: /opt/geoserver/data_dir/gwc/geowebcache-diskquota.xml
23 Jun 03:50:40 CONFIG [diskquota.DiskQuotaMonitor] - Setting up disk quota periodic enforcement task
23 Jun 03:50:40 CONFIG [diskquota.DiskQuotaMonitor] - 0 layers configured with their own quotas. 
23 Jun 03:50:40 CONFIG [diskquota.DiskQuotaMonitor] - 0 layers attached to global quota 20.0 GB
23 Jun 03:50:40 CONFIG [diskquota.DiskQuotaMonitor] - Disk quota periodic enforcement task set up every 5 SECONDS
23 Jun 03:50:40 INFO   [geoserver.security] - Strong cryptography is available
23 Jun 03:50:40 CONFIG [geoserver.security] - Start reloading user/groups for service named default
23 Jun 03:50:40 CONFIG [geoserver.security] - Reloading user/groups successful for service named default
23 Jun 03:50:40 INFO   [geoserver.security] - Start encrypting configuration passwords using strongPbePasswordEncoder
23 Jun 03:50:40 INFO   [geoserver.security] - End encrypting configuration passwords
23 Jun 03:50:40 INFO   [config.XMLConfiguration] - Wrote configuration to /opt/geoserver/data_dir/gwc
