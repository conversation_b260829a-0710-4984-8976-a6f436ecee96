version: '3.8'

services:
  geoserver:
    image: kartoza/geoserver:2.21.0
    container_name: geoserver
    ports:
      - "8091:8080"
    volumes:
      - /Users/<USER>/software/geoserver/data:/opt/geoserver/data_dir
      - /Users/<USER>/software/geoserver/drivers/mysql-connector-java-8.0.23.jar:/usr/local/tomcat/webapps/geoserver/WEB-INF/lib/mysql-connector-j.jar
      - /Users/<USER>/software/geoserver/drivers/gt-jdbc-mysql-27.0.jar:/usr/local/tomcat/webapps/geoserver/WEB-INF/lib/gt-jdbc-mysql.jar
    environment:
      - GEOSERVER_DATA_DIR=/opt/geoserver/data_dir
      - GEOWEBCACHE_CACHE_DIR=/opt/geoserver/data_dir/gwc
      - GEOSERVER_ADMIN_USER=admin
      - GEOSERVER_ADMIN_PASSWORD=geoserver
      - ENABLE_JSONP=true
      - MAX_FILTER_RULES=20
      - OPTIMIZE_LINE_WIDTH=false
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - geoserver-net
      -
networks:
  geoserver-net:
    driver: bridge