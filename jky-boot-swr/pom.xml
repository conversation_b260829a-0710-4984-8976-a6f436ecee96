<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jky-boot-parent</artifactId>
        <groupId>com.jky.boot</groupId>
        <version>3.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jky-boot-swr</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-base-core</artifactId>
        </dependency>
        <!--shiro-->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring-boot-starter</artifactId>
            <version>${shiro.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jky.modules.estar</groupId>
            <artifactId>jky-module-estar</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <!--Geometry工具库依赖-->
        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>1.19.0</version>
        </dependency>

    </dependencies>

</project>