package com.jky.dgswr.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.service.IDgswrReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作面信息上报管理
 */
@Validated
@Api(value = "工作面信息上报管理", tags = {"工作面信息上报管理"})
@RestController
@RequestMapping("/swr/report")
@RequiredArgsConstructor
public class DgswrReportController {

    private final IDgswrReportService dgswrReportService;

    /**
     * 查询工作面上报列表
     */
    @ApiOperation("查询工作面上报列表(dgswr-report-list)")
//    @RequiresPermissions("dgswr-report-list")
    @GetMapping("/list")
    public Result<List<DgswrProjectVo>> selectReportList(DgswrReportBo query) {
        List<DgswrProjectVo> list = dgswrReportService.selectReportList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询上报详情
     */
    @ApiOperation("查询上报详情(dgswr-report-query)")
    @RequiresPermissions("dgswr-report-query")
    @GetMapping("/{reportId}")
    public Result<DgswrReportVo> getReportDetail(@PathVariable("reportId") String reportId) {
        DgswrReportVo report = dgswrReportService.queryById(reportId);
        if (report == null) {
            Result<DgswrReportVo> result = new Result<>();
            result.error500("上报记录不存在");
            return result;
        }
        return Result.OK("查询成功", report);
    }

    /**
     * 新增上报
     */
    @ApiOperation("新增上报(dgswr-report-add)")
    @RequiresPermissions("dgswr-report-add")
    @PostMapping
    public Result<Boolean> addReport(@Validated @RequestBody DgswrReportBo bo) {
        return Result.OK("操作成功", dgswrReportService.insert(bo));
    }

    /**
     * 分页查询工作面上报历史记录
     */
    @ApiOperation("分页查询工作面上报历史记录(dgswr-report-history)")
    @RequiresPermissions("dgswr-report-history")
    @GetMapping("/history/{workfaceId}")
    public Result<Page<DgswrReportVo>> history(@PathVariable("workfaceId") String workfaceId,
                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<DgswrReportVo> list = dgswrReportService.selectWorkfaceReportHistoryPage(workfaceId, pageNo, pageSize);
        return Result.OK("查询成功", list);
    }
}
