package com.jky.dgswr.controller;

import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.dto.VipLoginDto;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.domain.vo.VipLoginVo;
import com.jky.dgswr.service.IDgswrReportService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import com.jky.dgswr.service.IH5Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/06/30
 * @Description:
 */
@Validated
@Api(value = "H5接口", tags = {"H5接口"})
@RestController
@RequestMapping("/swr/h5")
@RequiredArgsConstructor
public class H5Controller {


    private final IH5Service ih5Service;
    private final IDgswrReportService dgswrReportService;
    private final IDgswrWorkfaceService dgswrWorkfaceService;

    /**
     * 登录
     */
    @PostMapping("/login")
    public Result<VipLoginVo> login(HttpServletRequest request, @RequestBody VipLoginDto vipLoginDto) {
        return Result.OK(ih5Service.login(request, vipLoginDto));
    }


    /**
     * 查询工作面上报列表
     */
    @ApiOperation("查询工作面上报列表")
    @GetMapping("/workface/list")
    public Result<List<DgswrProjectVo>> selectReportList(DgswrReportBo query) {
        List<DgswrProjectVo> list = dgswrReportService.selectReportList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询工作面-基本信息
     */
    @ApiOperation("查询工作面-基本信息")
    @GetMapping("/workface/{workfaceId}")
    public Result<DgswrWorkfaceVo> getWorkfaceDetail(@PathVariable("workfaceId") String workfaceId) {
        DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(workfaceId);
        if (workface == null) {
            Result<DgswrWorkfaceVo> result = new Result<>();
            result.error500("工作面不存在");
            return result;
        }
        return Result.OK("查询成功", workface);
    }

    /**
     * 新增上报
     */
    @ApiOperation("新增上报")
    @PostMapping("/report")
    public Result<Boolean> addReport(@Validated @RequestBody DgswrReportBo bo) {
        return Result.OK("操作成功", dgswrReportService.insert(bo));
    }

    /**
     * 查询工作面上报历史记录
     */
    @ApiOperation("查询工作面上报历史记录")
    @GetMapping("/report/history/{workfaceId}")
    public Result<List<DgswrReportVo>> history(@PathVariable("workfaceId") String workfaceId) {
        List<DgswrReportVo> list = dgswrReportService.selectWorkfaceReportHistoryList(workfaceId);
        return Result.OK("查询成功", list);
    }


}
