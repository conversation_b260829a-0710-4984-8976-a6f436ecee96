package com.jky.dgswr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgswr.domain.DgswrProject;
import com.jky.dgswr.domain.DgswrWorkface;
import com.jky.dgswr.domain.bo.DgswrProjectBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.mapper.DgswrProjectMapper;
import com.jky.dgswr.service.IDgswrProjectService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/06/16
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class DgswrProjectServiceImpl extends ServiceImpl<DgswrProjectMapper, DgswrProject> implements IDgswrProjectService {

    private final IDgswrWorkfaceService dgswrWorkfaceService;

    @Override
    public List<DgswrProjectVo> selectProjectList(DgswrProjectBo query) {
        List<DgswrProject> dgswrProjects = baseMapper.selectList(Wrappers.<DgswrProject>query()
                .lambda()
                .eq(StringUtils.isNotBlank(query.getStatus()), DgswrProject::getStatus, query.getStatus())
                .like(StringUtils.isNotBlank(query.getProjectName()), DgswrProject::getProjectName, query.getProjectName())
                .orderByDesc(DgswrProject::getCreateTime));

        List<DgswrProjectVo> dgswrProjectVos = BeanUtil.copyToList(dgswrProjects, DgswrProjectVo.class);

        for (DgswrProjectVo dgswrProjectVo : dgswrProjectVos) {
            DgswrWorkfaceBo workfaceBo = new DgswrWorkfaceBo();
            workfaceBo.setWorkfaceName(query.getWorkfaceName());
            workfaceBo.setProjectId(dgswrProjectVo.getProjectId());
            List<DgswrWorkfaceVo> dgswrWorkfaceVos = dgswrWorkfaceService.selectWorkfaceList(workfaceBo);
            dgswrProjectVo.setWorkfaceList(BeanUtil.copyToList(dgswrWorkfaceVos, DgswrWorkfaceReportVo.class));
        }

        return dgswrProjectVos;
    }

    @Override
    public DgswrProjectVo queryById(String projectId) {
        DgswrProject dgswrProject = baseMapper.selectById(projectId);
        return BeanUtil.copyProperties(dgswrProject, DgswrProjectVo.class);
    }

    @Override
    public Boolean insert(DgswrProjectBo projectBo) {
        DgswrProject dgswrProject = BeanUtil.copyProperties(projectBo, DgswrProject.class);
        return baseMapper.insert(dgswrProject) > 0;
    }

    @Override
    public Boolean update(DgswrProjectBo projectBo) {
        DgswrProject dgswrProject = BeanUtil.copyProperties(projectBo, DgswrProject.class);
        return baseMapper.updateById(dgswrProject) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(String projectId) {

        boolean exists = dgswrWorkfaceService.exists(Wrappers.<DgswrWorkface>lambdaQuery().eq(DgswrWorkface::getProjectId, projectId));
        if (exists) {
            throw new RuntimeException("项目下存在工作面，不予删除");
        }
        return baseMapper.deleteById(projectId) > 0;
    }
}
