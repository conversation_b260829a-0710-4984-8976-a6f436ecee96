package com.jky.dgswr.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgswr.domain.DgswrProject;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.DgswrWorkface;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.handler.IdGeneratorHelper;
import com.jky.dgswr.mapper.DgswrProjectMapper;
import com.jky.dgswr.mapper.DgswrReportMapper;
import com.jky.dgswr.mapper.DgswrWorkfaceMapper;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import lombok.RequiredArgsConstructor;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作面管理Service实现类
 */
@Service
@RequiredArgsConstructor
public class DgswrWorkfaceServiceImpl extends ServiceImpl<DgswrWorkfaceMapper, DgswrWorkface> implements IDgswrWorkfaceService {

    private final DgswrProjectMapper dgswrProjectMapper;
    private final DgswrReportMapper dgswrReportMapper;

    @Override
    public List<DgswrWorkfaceVo> selectWorkfaceList(DgswrWorkfaceBo query) {
        LambdaQueryWrapper<DgswrWorkface> wrapper = Wrappers.<DgswrWorkface>lambdaQuery();

        if (StringUtils.isNotBlank(query.getProjectId())) {
            wrapper.eq(DgswrWorkface::getProjectId, query.getProjectId());
        }

        if (StringUtils.isNotBlank(query.getWorkfaceName())) {
            wrapper.like(DgswrWorkface::getWorkfaceName, query.getWorkfaceName());
        }

        if (StringUtils.isNotBlank(query.getAddress())) {
            wrapper.like(DgswrWorkface::getAddress, query.getAddress());
        }
        wrapper.orderByDesc(DgswrWorkface::getCreateTime);
        List<DgswrWorkface> list = baseMapper.selectList(wrapper);

        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public DgswrWorkfaceVo queryById(String workfaceId) {
        DgswrWorkface workface = baseMapper.selectById(workfaceId);
        if (workface == null) {
            return null;
        }
        DgswrWorkfaceVo dgswrWorkfaceVo = convertToVo(workface);
        DgswrProject dgswrProject = dgswrProjectMapper.selectById(workface.getProjectId());
        if (dgswrProject != null) {
            dgswrWorkfaceVo.setProjectName(dgswrProject.getProjectName());
        }
        LambdaQueryWrapper<DgswrReport> wrapper = Wrappers.<DgswrReport>lambdaQuery();
        wrapper.eq(DgswrReport::getWorkfaceId, workface.getWorkfaceId());
        wrapper.orderByDesc(DgswrReport::getReportTime);
        wrapper.last("LIMIT 1");

        DgswrReport latestReport = dgswrReportMapper.selectOne(wrapper);
        if (latestReport != null) {
            dgswrWorkfaceVo.setLatestReportId(latestReport.getReportId());
            dgswrWorkfaceVo.setLatestReportTime(latestReport.getReportTime());
            dgswrWorkfaceVo.setReportContent(latestReport.getReportContent());
            dgswrWorkfaceVo.setReportImages(latestReport.getReportImages());
            dgswrWorkfaceVo.setReportUnit(latestReport.getReportUnit());
            dgswrWorkfaceVo.setReportUserName(latestReport.getReportUserName());
        }
        return dgswrWorkfaceVo;
    }

    @Override
    @Transactional
    public Boolean insert(DgswrWorkfaceBo bo) {
        DgswrWorkface workface = new DgswrWorkface();
        BeanUtils.copyProperties(bo, workface);
        workface.setIsReported(false);
        String id = IdGeneratorHelper.next();
        //图层ID
        workface.setLayerId(id);
        //工作面ID
        workface.setWorkfaceId(id);
        workface.setCreateTime(DateUtil.date());
        workface.setUpdateTime(DateUtil.date());
        if (StringUtils.isNotBlank(bo.getLon()) && StringUtils.isNotBlank(bo.getLat())) {
            GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel());
            Point geometry = geometryFactory.createPoint(new Coordinate(Double.parseDouble(bo.getLon()), Double.parseDouble(bo.getLat())));
            workface.setGeom(geometry);
        }
        int result = baseMapper.insertGeometry(workface);
        return result > 0;
    }

    @Override
    public Boolean update(DgswrWorkfaceBo bo) {
        DgswrWorkface workface = new DgswrWorkface();
        BeanUtils.copyProperties(bo, workface);

        int result = baseMapper.updateById(workface);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(String workfaceId) {
        int result = baseMapper.deleteById(workfaceId);
        if (result > 0) {
            dgswrReportMapper.delete(Wrappers.<DgswrReport>lambdaQuery().eq(DgswrReport::getWorkfaceId, workfaceId));
        }
        return result > 0;
    }

    @Override
    public List<DgswrWorkfaceVo> selectWorkfaceListByProjectId(String projectId) {
        LambdaQueryWrapper<DgswrWorkface> wrapper = Wrappers.<DgswrWorkface>lambdaQuery();
        wrapper.eq(DgswrWorkface::getProjectId, projectId);

        List<DgswrWorkface> list = baseMapper.selectList(wrapper);

        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public Boolean updateReportStatus(String workfaceId, Boolean isReported) {
        if (StringUtils.isBlank(workfaceId)) {
            return false;
        }
        DgswrWorkface dgswrWorkface = baseMapper.selectById(workfaceId);
        if (dgswrWorkface != null && dgswrWorkface.getIsReported()) {
            throw new RuntimeException("本月已上报，无需上报！");
        }
        int result = baseMapper.update(null, Wrappers.<DgswrWorkface>lambdaUpdate()
                .eq(DgswrWorkface::getWorkfaceId, workfaceId)
                .set(DgswrWorkface::getIsReported, isReported));
        return result > 0;
    }

    @Override
    @Transactional
    public Boolean resetAllReportStatus() {
        int result = baseMapper.update(null, Wrappers.<DgswrWorkface>lambdaUpdate()
                .set(DgswrWorkface::getIsReported, false));
        return result > 0;
    }

    /**
     * 实体转换为VO
     */
    private DgswrWorkfaceVo convertToVo(DgswrWorkface workface) {
        DgswrWorkfaceVo vo = new DgswrWorkfaceVo();
        BeanUtils.copyProperties(workface, vo);
        return vo;
    }
}
