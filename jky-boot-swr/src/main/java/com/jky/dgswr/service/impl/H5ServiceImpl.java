package com.jky.dgswr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.dgswr.domain.SmzUser;
import com.jky.dgswr.domain.dto.VipLoginDto;
import com.jky.dgswr.domain.vo.VipLoginVo;
import com.jky.dgswr.mapper.SmzUserMapper;
import com.jky.dgswr.service.IH5Service;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: lpg
 * @Date: 2025/06/30
 * @Description:
 */
@RequiredArgsConstructor
@Service
public class H5ServiceImpl implements IH5Service {

    private final SmzUserMapper smzUserMapper;
    private final RedisUtil redisUtil;
    /**
     * client_id 和请求头的vipApiKey是固定值，用于识别是从城建app进来数字工地H5的标志
     */
    private final String vipApiKey = "vipH5-5Uyy1ydhpPTLPN82f";
    private final String clientId = "viph5_202506ef052eaff2ce4d5fb004edb";

    @Override
    public SmzUser queryMultiUserInfo(String phone, Integer userType) {
        SmzUser user = null;

        LambdaQueryWrapper<SmzUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmzUser::getPhone, phone)
                .last("limit 1");
        if (CommonConstant.USER_TYPE_VIP_APP.equals(userType)) {
            user = smzUserMapper.selectOne(queryWrapper);
        } else {
            user = smzUserMapper.selectOne(queryWrapper);
        }
        return user;
    }

    @Override
    @SneakyThrows
    public VipLoginVo login(HttpServletRequest request, VipLoginDto bo) {

        //判断是否是开发环境
        if (!SpringContextHolder.getBean(Environment.class).getActiveProfiles()[0].equals("dev")) {
            String providedApiKey = request.getHeader("vipApiKey");

            if (!vipApiKey.equals(providedApiKey)) {
                throw new Exception("无效的请求头");
            }
        }
        SmzUser sysUser = smzUserMapper.selectOne(Wrappers.<SmzUser>lambdaQuery().eq(SmzUser::getPhone, bo.getPhone()).last("limit 1"));

        if (sysUser == null || !sysUser.getPhone().equals(bo.getPhone())) {
            throw new Exception("登录用户不存在！");
        }

        String baseUrl = "https://vipgd.dgjky.com/";
        String token = JwtUtil.sign(sysUser.getPhone() + "_" + sysUser.getId(), sysUser.getId());
        // 设置token缓存有效时间
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.set(CommonConstant.ENTRY_USER_TYPE + sysUser.getPhone() + "_" + sysUser.getId(), CommonConstant.USER_TYPE_VIP_APP);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);

        VipLoginVo vo = new VipLoginVo();
        vo.setUrl(baseUrl + bo.getResponsePageType() + "?client_id=" + clientId + "&token=" + token);
        vo.setSmzUser(sysUser);
        return vo;
    }
}
