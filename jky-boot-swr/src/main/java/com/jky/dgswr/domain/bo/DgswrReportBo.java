package com.jky.dgswr.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 上报内容表BO
 */
@Data
public class DgswrReportBo implements Serializable {

    /**
     * 上报ID
     */
    private String reportId;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工作面ID
     */
    @NotBlank(message = "工作面ID不能为空")
    private String workfaceId;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 工作面介绍
     */
    private String introduction;

    /**
     * 工作面详情
     */
    private String details;

    /**
     * 上报内容
     */
    @NotBlank(message = "上报内容不能为空")
    private String reportContent;

    /**
     * 上报图片（多个图片用逗号分隔）
     */
    private String reportImages;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 上报用户ID
     */
    private Long reportUserId;

    /**
     * 上报人姓名
     */
    private String reportUserName;

    /**
     * 上报单位
     */
    @NotBlank(message = "上报单位不能为空")
    private String reportUnit;

    /**
     * 备注
     */
    private String remark;

    // ========== 查询条件字段 ==========

    /**
     * 上报日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String reportDate;

    /**
     * 本月是否已上报
     */
    private Boolean isReported;
}
