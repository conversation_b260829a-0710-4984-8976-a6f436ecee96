package com.jky.dgswr.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作面表
 */
@Data
@TableName("dgswr_workface")
public class DgswrWorkface implements Serializable {

    /**
     * 工作面ID
     */
    @TableId(value = "workface_id")
    private String workfaceId;

    /**
     * 图层ID 与工作面ID相同
     */
    private String layerId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 地址
     */
    private String address;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lon;

    /**
     * 几何对象信息
     */
//    @TableField(value = "geom", jdbcType = JdbcType.BLOB, typeHandler = GeometryTypeHandler.class)
    private Geometry geom;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工作面介绍
     */
    private String introduction;

    /**
     * 工作面详情
     */
    private String details;

    /**
     * 本月是否已上报
     */
    private Boolean isReported;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
    private String createBy;
    private String updateBy;
}
