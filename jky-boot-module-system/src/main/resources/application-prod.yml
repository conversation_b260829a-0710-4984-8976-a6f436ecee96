server:
  port: 8181
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /jky-boot
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL    #modify by jky for oracle
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 多数据源配置
        master:
          url: jdbc:mysql://**************:13306/jky-dggddoc?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&autoReconnect=true
          username: root
          password: ZHSZ@2024!data
          driver-class-name: com.mysql.cj.jdbc.Driver
        dgzjjsp:
          url: jdbc:mysql://**************:23306/dgzjjsp_dev?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&connectTimeout=10000&socketTimeout=30000&autoReconnect=true
          username: root
          password: TN@AG!7b_&
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 城建
        userVip:
          url: jdbc:mysql://**************:23306/vip_ics_zhgd?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&connectTimeout=10000&socketTimeout=30000&autoReconnect=true
          username: root
          password: TN@AG!7b_&
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 莞e住
        userZtf:
          url: jdbc:mysql://**************:23306/ztf_ics_dgzj?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&connectTimeout=10000&socketTimeout=30000&autoReconnect=true
          username: root
          password: TN@AG!7b_&
          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: *************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 7
    host: **************
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: zhsz,./123
    port: 29080
  #大屏用数据源连接池配置
  druid:
    initial-size: 10 # 初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
    min-idle: 10 # 最小连接池数量
    maxActive: 200 # 最大连接池数量
    maxWait: 3000 # 获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置
    timeBetweenEvictionRunsMillis: 60000 # 关闭空闲连接的检测时间间隔.Destroy线程会检测连接的间隔时间，如果连接空闲时间大于等于minEvictableIdleTimeMillis则关闭物理连接。
    minEvictableIdleTimeMillis: 300000 # 连接的最小生存时间.连接保持空闲而不被驱逐的最小时间
    testWhileIdle: true # 申请连接时检测空闲时间，根据空闲时间再检测连接是否有效.建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRun
    poolPreparedStatements: true # 开启PSCache
    maxPoolPreparedStatementPerConnectionSize: 20 #设置PSCache值
    connectionErrorRetryAttempts: 3 # 连接出错后再尝试连接三次
    breakAfterAcquireFailure: true # 数据库服务宕机自动重连机制
    timeBetweenConnectErrorMillis: 300000 # 连接出错后重试时间间隔
  #网盘 异步线程池
  async-thread-pool:
    #异步线程池组件开关，默认false
    enable: true
    #核心线程数,默认：Java虚拟机可用线程数
    core-pool-size: 8
    #线程池最大线程数,默认：40000
    max-pool-size: 40000
    #线程队列最大线程数,默认：80000
    queue-capacity: 80000
    #自定义线程名前缀，默认：Async-ThreadPool-
    thread-name-prefix: Async-ThreadPool-
    #线程池中线程最大空闲时间，默认：60，单位：秒
    keep-alive-seconds: 60
    #核心线程是否允许超时，默认false
    allow-core-thread-time-out: false
    #IOC容器关闭时是否阻塞等待剩余的任务执行完成，默认:false（必须设置setAwaitTerminationSeconds）
    wait-for-tasks-to-complete-on-shutdown: false
    #阻塞IOC容器关闭的时间，默认：10秒（必须设置setWaitForTasksToCompleteOnShutdown）
    await-termination-seconds: 10
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,classpath*:com/jky/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
    #mybatis-plus出现两个空行问题，后续版本应该会修正，临时先加这个解决与JSqlParser4.6冲突问题
    shrink-whitespaces-in-sql: true

#jeecg专用配置
minidao:
  base-package: org.jeecg.modules.jmreport.*
  #DB类型（mysql | postgresql | oracle | sqlserver| other）
  db-type: mysql
jky:
  # 是否启用安全模式
  safeMode: false
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: nbcioestar05f1c54d63749eda95f9fa6d49v442aestarnbcio
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: minio
  path:
    #文件上传根目录 设置
    upload: /opt/upFiles
    #  项目申报表上传文件
    regfromUpload: /www/wwwroot/gddoc/uplooadFiles
    #webapp文件路径
    webapp: /opt/webapp
  shiro:
    excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/category/**,/visual/**,/map/**,/jmreport/bigscreen2/**,/swr/**,/sys/common/upload,/sys/common/download
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: ??
    secretKey: ??
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: jkydev
  # ElasticSearch 6设置
  elasticsearch:
    cluster-name: jky-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: false
  # 表单设计器配置
  desform:
    # 主题颜色（仅支持 16进制颜色代码）
    theme-color: "#1890ff"
    # 文件、图片上传方式，可选项：qiniu（七牛云）、system（跟随系统配置）
    upload-type: system
    map:
      # 配置百度地图的AK，申请地址：https://lbs.baidu.com/apiconsole/key?application=key#/home
      baidu: ??
  # 在线预览文件服务器地址配置
  file-view-domain: 127.0.0.1:8012
  # minio文件上传
  minio:
    minio_url: **************:9001
    minio_name: admin
    minio_pass: syzhkj123.0
    bucketName: lib
  #大屏报表参数设置
  jmreport:
    mode: dev
    #数据字典是否进行saas数据隔离，自己看自己的字典
    saas: false
    #是否需要校验token
    is_verify_token: true
    #必须校验方法
    verify_methods: remove,delete,save,add,update
  #Wps在线文档
  wps:
    domain: https://wwo.wps.cn/office/
    appid: ??
    appsecret: ??
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jky/job/jobhandler/
    logRetentionDays: 30
  route:
    config:
      data-id: jky-gateway-router
      group: DEFAULT_GROUP
      #自定义路由配置 yml nacos database
      data-type: database
  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper: info
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: false
    username: jky
    password: jky4321
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jky-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jky-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jky-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jky-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
#第三方APP对接
third-app:
  enabled: true
  type:
    #企业微信
    WECHAT_ENTERPRISE:
      enabled: false
      #CORP_ID
      client-id: ??
      #SECRET
      client-secret: ??
      #自建应用id
      agent-id: ??
      #自建应用秘钥（新版企微需要配置）
      # agent-app-secret: ??
    #钉钉
    DINGTALK:
      enabled: true
      # appKey
      client-id: dingrclmpcbhnquxtpsi
      # appSecret
      client-secret: UT9T-zTD82wqMZc_IJl5qoSUaijoy1_YvN1MKU_7a-_cf9SEbB9erZpZ8wq9HKrl
      agent-id: 1105646298
flowable:
  process:
    definition-cache-limit: -1
  database-schema-update: true
  activity-font-name: 宋体
  label-font-name: 宋体
  annotation-font-name: 宋体
  #关闭定时任务JOB
  async-executor-activate: false
  #消息链接基地址
  message-base-url: http://localhost:9888/flowable/task/record/index
bigscreen:
  downloadPath: http:/127.0.0.1:9010/jky/bs
  file:
    #导入导出临时文件夹 默认.代表当前目录，拼接/tmp_zip/目录
    tmpPath: .
jkyDisk: #网盘
  bucketName: ${jky.minio.bucketName}
  storageType: ${jky.uploadType}
  localStoragePath: ${jky.path.upload}
  aliyun:
    endpoint: ${jky.oss.endpoint}
    accessKeyId: ${jky.oss.accessKey}
    accessKeySecret: ${jky.oss.secretKey}
    bucketName: ${jky.oss.bucketName}
    objectName:
  thumbImage:
    width: 150
    height: 150
  minio:
    endpoint: ${jky.minio.minio_url}
    accessKey: ${jky.minio.minio_name}
    secretKey: ${jky.minio.minio_pass}
    bucketName: netdisk
  # 当前部署外网IP，用于office预览
  deployment:
    host: *************
  files: #onlyoffice配置参数
    filesize-max: 5242880
    storage: *************
    folder: documents
    docservice:
      fillforms-docs: .oform|.docx
      viewed-docs: .pdf|.djvu|.xps|.oxps
      edited-docs: .docx|.xlsx|.csv|.pptx|.txt|.docxf
      convert-docs: .docm|.dotx|.dotm|.dot|.doc|.odt|.fodt|.ott|.xlsm|.xlsb|.xltx|.xltm|.xlt|.xls|.ods|.fods|.ots|.pptm|.ppt|.ppsx|.ppsm|.pps|.potx|.potm|.pot|.odp|.fodp|.otp|.rtf|.mht|.html|.htm|.xml|.epub|.fb2
      timeout: 120000
      history.postfix: -hist
      url:
        site: http://*************:9831/ #onlyoffice端口号
        converter: ConvertService.ashx
        command: coauthoring/CommandService.ashx
        api: web-apps/apps/api/documents/api.js
        preloader: web-apps/apps/api/documents/cache-scripts.html
        example:
      secret: secret
      header: Authorization
      verify-peer-off: true
      languages: en:English|hy:Armenian|az:Azerbaijani|eu:Basque|be:Belarusian|bg:Bulgarian|ca:Catalan|zh:Chinese (People's Republic of China)|zh-TW:Chinese (Traditional, Taiwan)|cs:Czech|da:Danish|nl:Dutch|fi:Finnish|fr:French|gl:Galego|de:German|el:Greek|hu:Hungarian|id:Indonesian|it:Italian|ja:Japanese|ko:Korean|lv:Latvian|lo:Lao|ms:Malay (Malaysia)|nb:Norwegian|pl:Polish|pt:Portuguese (Brazil)|pt-PT:Portuguese (Portugal)|ro:Romanian|ru:Russian|sk:Slovak|sl:Slovenian|es:Spanish|sv:Swedish|tr:Turkish|uk:Ukrainian|vi:Vietnamese
# 对外接口
outapi:
  gddoc:
    baseurl: http://***********:9888/jkyapi/
    apikey: gddoc-wn6PI68cIXaGyE7l6wLxX

