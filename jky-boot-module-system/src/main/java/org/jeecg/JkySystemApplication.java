package org.jeecg;

import com.dtflys.forest.springboot.annotation.ForestScan;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.UnknownHostException;

/**
 * 单体启动类（采用此类启动为单体模式）
 */
@EnableScheduling
@Slf4j
@ForestScan(basePackages = "com.jky")
@SpringBootApplication(scanBasePackages = {"org.jeecg", "com.jky"},
        exclude = {MongoAutoConfiguration.class, MongoMetricsAutoConfiguration.class}
)
@MapperScan(basePackages = {"com.jky.**.mapper", "com.jky.modules.**.mapper"})
public class JkySystemApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(JkySystemApplication.class);
    }

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(JkySystemApplication.class, args);
        log.info("jky-boot is running success");
//        Environment env = application.getEnvironment();
//        String ip = InetAddress.getLocalHost().getHostAddress();
//        String port = env.getProperty("server.port");
//        String path = oConvertUtils.getString(env.getProperty("server.servlet.context-path"));
//        log.info("\n----------------------------------------------------------\n\t" +
//                "Application JKY-BOOT is running! Access URLs:\n\t" +
//                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
//                "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
//                "Swagger文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
//                "----------------------------------------------------------");

    }

}